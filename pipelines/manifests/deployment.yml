apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    k8s-app: tsc-kpi-catalog-api
    tags.datadoghq.com/env: "${environmentAcronym}" # Unified service tag - Deployment Env tag
    tags.datadoghq.com/service: "tsc-kpi-catalog-api" # Unified service tag - Deployment Service tag
    tags.datadoghq.com/version: "1.0" # Unified service tag - Deployment Version tag
  name: tsc-kpi-catalog-api
  namespace: ${k8sNamespace}
  selfLink: /apis/apps/v1/namespaces/${k8sNamespace}/deployments/tsc-kpi-catalog-api
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      k8s-app: tsc-kpi-catalog-api
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        k8s-app: tsc-kpi-catalog-api
        tags.datadoghq.com/env: "${environmentAcronym}" # Unified service tag - Deployment Env tag
        tags.datadoghq.com/service: "tsc-kpi-catalog-api" # Unified service tag - Deployment Service tag
        tags.datadoghq.com/version: "2.0" # Unified service tag - Deployment Version tag
        admission.datadoghq.com/enabled: "true"
      annotations:
        admission.datadoghq.com/js-lib.version: "v3.17.1"
      name: tsc-kpi-catalog-api
    spec:
      volumes:
        - name: volume
          persistentVolumeClaim:
            claimName: "${volumeName}"
      containers:
        - name: tsc-kpi-catalog-api
          image: peopleproductsacr.azurecr.io/tsc/kpi-catalog-api:${tag}
          imagePullPolicy: Always
          ports:
            - containerPort: 2135
          env:
            - name: APP_ENV
              value: "${environmentAcronym}"
            - name: DD_ENV
              value: "${environmentAcronym}"
            - name: NODE_ENV
              value: container
            - name: DATABASE_HOST
              valueFrom:
                secretKeyRef:
                  key: db-host
                  name: db-host
            - name: DATABASE_PORT
              valueFrom:
                secretKeyRef:
                  key: db-port
                  name: db-port
            - name: DATABASE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: ${dbUser}
                  name: ${dbUser}
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: ${dbPass}
                  name: ${dbPass}
            - name: DATABASE_NAME
              valueFrom:
                secretKeyRef:
                  key: db-name
                  name: db-name
            - name: PORT
              value: '2135'
            - name: REDIS_HOST
              value: redis
            - name: REDIS_PORT
              value: '6379'
            - name: SESSION_SECRET
              valueFrom:
                secretKeyRef:
                  key: session-secret
                  name: session-secret
            - name: SESSION_NAME
              value: "str_token"
            - name: SESSION_SECURE
              value: 'false'
            - name: FILE_DIR
              value: '/files'
            - name: CSURF_SECRET
              valueFrom:
                secretKeyRef:
                  key: csurf-secret
                  name: csurf-secret
          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: 500m
              memory: 16Gi
          volumeMounts:
            - name: volume
              mountPath: /files
          livenessProbe:
            httpGet:
              path: /health-check
              port: 2135
              scheme: HTTP
            initialDelaySeconds: 15
            timeoutSeconds: 1
            periodSeconds: 60
            successThreshold: 1
            failureThreshold: 3
          securityContext:
            runAsUser: 10000
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            readOnlyRootFilesystem: true
            privileged: false
            capabilities:
              drop:
                - ALL
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
