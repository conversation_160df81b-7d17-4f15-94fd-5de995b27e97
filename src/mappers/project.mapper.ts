import { GetProjectResponse } from 'src/dtos/responses/get-project.dto';
import { ProjectType } from 'src/enums';
import { ProjectEntity } from '../entities/project.entity';
import { DeliverableEntity } from '../entities/deliverable.entity';

export function mapProjectToView(entity: ProjectEntity): GetProjectResponse {
  return {
    uid: entity.uid,
    name: entity.name,
    function: entity.function,
    objective: entity.objective ?? '',
    project_type: entity.project_type as ProjectType,
    date_start: entity.date_start ?? null,
    date_end: entity.date_end ?? null,
    deliverables: entity.deliverables?.map((d: DeliverableEntity) => d.uid) || [],
  };
}
