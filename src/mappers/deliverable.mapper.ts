import { DeliverableViewDto } from 'src/dtos/responses/deliverable-view.dto';
import { DeliverableEntity } from '../entities/deliverable.entity';
import { ProjectEntity } from '../entities/project.entity';

export function mapToView(entity: DeliverableEntity | ProjectEntity): DeliverableViewDto {
  const isProject = 'deliverables' in entity;

  const base: DeliverableViewDto = {
    uid: entity.uid,
    name: entity.name,
    function: entity.function,
    frequency: entity.frequency,
    is_active: entity.is_active,
    usage: entity.usage ?? Math.ceil(Math.random() * 100),
    source: entity.source,
    owners: (entity as any).owners ?? [],
    calculation_method: (entity as any).calculation_method ?? '',
    definition: (entity as any).definition ?? '',
    pa_value: (entity as any).pa_value ?? '',
    bu_level_aggregation: (entity as any).bu_level_aggregation ?? '',
    type: (entity as any).deliverable_type?.code,
    project_type: (entity as any).project_type,
  };

  if (isProject) {
    return {
      ...base,
      objective: (entity as any).objective ?? '',
      date_start: (entity as any).date_start ?? null,
      date_end: (entity as any).date_end ?? null,
      deliverables: ((entity as any).deliverables ?? []).map((d: any) => mapToView(d)),
    };
  }

  return base;
}