import 'reflect-metadata';
import * as dotenv from 'dotenv';
import { DataSource } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { DeliverableTypeEntity } from '../entities/deliverable-type.entity';
import { DeliverableUsageEntity } from '../entities/deliverable-usage.entity';
import { ProjectEntity } from '../entities/project.entity';
import { DeliverableEntity } from '../entities/deliverable.entity';

dotenv.config({ path: '.env' });

export const dataSource = new DataSource({
  type: 'mssql',
  host: process.env.DATABASE_HOST,
  port: Number(process.env.DATABASE_PORT),
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  schema: process.env.DATABASE_SCHEMA,
  synchronize: false,
  options: { encrypt: true },
  namingStrategy: new SnakeNamingStrategy(),
  entities: [
    DeliverableEntity,
    ProjectEntity,
    DeliverableTypeEntity,
    DeliverableUsageEntity
  ],
});
