import { registerAs } from '@nestjs/config';
import { config as dotenvConfig } from 'dotenv';
import { DeliverableTypeEntity } from '../entities/deliverable-type.entity';
import { DeliverableEntity } from '../entities/deliverable.entity';
import { ProjectEntity } from '../entities/project.entity';
import { DeliverableUsageEntity } from '../entities/deliverable-usage.entity';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

dotenvConfig({ path: '.env' });

const config = {
  type: 'mssql',
  host: `${process.env.DATABASE_HOST}`,
  port: +process.env.DATABASE_PORT,
  username: `${process.env.DATABASE_USERNAME}`,
  password: `${process.env.DATABASE_PASSWORD}`,
  database: `${process.env.DATABASE_NAME}`,
  schema: `${process.env.DATABASE_SCHEMA}`,
  synchronize: false,
  options: { encrypt: true },
  namingStrategy: new SnakeNamingStrategy(),
  entities: [
    DeliverableEntity,
    ProjectEntity,
    DeliverableTypeEntity,
    DeliverableUsageEntity
  ],
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

export default registerAs('typeorm', () => config);
