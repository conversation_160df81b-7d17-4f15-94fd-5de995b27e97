import { Request, Response, NextFunction } from 'express';
import { randomBytes, createCipheriv, randomUUID } from 'crypto';

const ALGORITHM = 'aes-256-cbc';

const encryptCookie = (cookie, _secret) => {
  /**
   * Encrypt a cookie using AES 256 bits
   * @param {cookie} string the cookie we want to encrypt. Will be visible as plain string to client.
   * @param {_secret} string the secret that will be stored server-side. Client will never see this.
   */
  const iv = randomBytes(16);
  const _cipher = createCipheriv(ALGORITHM, Buffer.from(_secret), iv);
  const encrypted = [
    iv.toString('hex'),
    ':',
    _cipher.update(cookie, 'utf8', 'hex'),
    _cipher.final('hex'),
  ];
  return encrypted.join('');
};

export const csrf =
  (secret: string, ignoredRoutes: string[]) =>
  (
    req: Request & { csrfToken: () => string; session: { csrfToken: string } },
    res: Response,
    next: NextFunction,
  ) => {
    if (ignoredRoutes.some((route) => req.path === route)) return next();

    if (!req.session)
      throw new Error(
        'Session middleware must be used before CSRF middleware.',
      );

    if (!req.session?.csrfToken)
      req.csrfToken = () => {
        const csrfToken = randomUUID();
        res.cookie('csrfToken', encryptCookie(csrfToken, secret), {
          httpOnly: true,
          sameSite: 'strict',
          signed: true,
          maxAge: 300000,
        });
        return csrfToken;
      };

    res.cookie('csrfToken', req.session?.csrfToken);
    next();
  };
