import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNumber } from 'class-validator';
import { TransformNumber } from '../../helpers/tranformer.helper';

export class PaginatedRequest {
  @IsNumber()
  @ApiProperty({
    description: 'Page number for pagination (1-based)',
    example: 1,
    minimum: 1
  })
  @Transform(TransformNumber)
  pageNumber: number;

  @IsNumber()
  @ApiProperty({
    description: 'Number of items to return per page',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @Transform(TransformNumber)
  pageSize: number;
}
