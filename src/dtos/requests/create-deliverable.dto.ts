import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OwnerDto } from 'src/dtos/owner.dto';
import { ProjectType, DeliverableType } from '../../enums';

export class CreateDeliverableRequest {
  @ApiProperty({
    description: 'Name of the deliverable',
    example: 'Monthly Revenue Report'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Business function or department this deliverable belongs to',
    example: 'Finance'
  })
  @IsString()
  function: string;

  @ApiProperty({
    description: 'How often this deliverable is generated or updated',
    example: 'Monthly'
  })
  @IsString()
  frequency: string;

  @ApiProperty({
    description: 'Source system or process that generates this deliverable',
    example: 'SAP Financial System'
  })
  @IsString()
  source: string;

  @ApiProperty({
    description: 'Whether this deliverable is currently active and in use',
    example: true
  })
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({
    description: 'Usage score or priority level of this deliverable',
    example: 85
  })
  @IsNumber()
  usage: number;

  @ApiPropertyOptional({
    description: 'Method or formula used to calculate this deliverable',
    example: 'Sum of all revenue streams minus returns and discounts'
  })
  @IsOptional()
  @IsString()
  calculation_method?: string;

  @ApiPropertyOptional({
    description: 'Detailed definition and purpose of this deliverable',
    example: 'Monthly report showing total revenue generated across all business units'
  })
  @IsOptional()
  @IsString()
  definition?: string;

  @ApiPropertyOptional({
    description: 'Performance Analytics value or target for this deliverable',
    example: '$10M monthly target'
  })
  @IsOptional()
  @IsString()
  pa_value?: string;

  @ApiPropertyOptional({
    description: 'How this deliverable is aggregated at business unit level',
    example: 'Sum across all BUs'
  })
  @IsOptional()
  @IsString()
  bu_level_aggregation?: string;

  @ApiPropertyOptional({
    description: 'Type classification of this deliverable',
    enum: DeliverableType,
    example: DeliverableType.KPI
  })
  @IsOptional()
  @IsEnum(DeliverableType)
  type?: DeliverableType;

  @ApiPropertyOptional({
    description: 'List of owners responsible for this deliverable',
    type: [OwnerDto],
    example: [
      { name: 'John Doe', email: '<EMAIL>', role: 'Data Analyst' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OwnerDto)
  @IsOptional()
  owners?: OwnerDto[];

  @ApiPropertyOptional({
    description: 'Business objective or goal this deliverable supports',
    example: 'Increase quarterly revenue by 15%'
  })
  @IsOptional()
  @IsString()
  objective?: string;

  @ApiPropertyOptional({
    description: 'Type of project this deliverable belongs to (if applicable)',
    enum: ProjectType,
    example: ProjectType.SIMPLE
  })
  @IsOptional()
  @IsEnum(ProjectType)
  project_type?: ProjectType;

  @ApiPropertyOptional({
    description: 'Array of deliverable UIDs that are associated with this deliverable',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '987fcdeb-51a2-43d1-9f4e-123456789abc']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  deliverables?: string[];

  @ApiPropertyOptional({
    description: 'Start date for this deliverable (if time-bound)',
    type: Date,
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_start?: Date;

  @ApiPropertyOptional({
    description: 'End date for this deliverable (if time-bound)',
    type: Date,
    example: '2024-12-31T23:59:59.999Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_end?: Date;
}
