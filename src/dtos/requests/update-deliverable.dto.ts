import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { OwnerDto } from 'src/dtos/owner.dto';

export class UpdateDeliverableRequest {
  @ApiPropertyOptional({
    description: 'Name of the deliverable',
    example: 'Monthly Revenue Report - Updated'
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Business function or department this deliverable belongs to',
    example: 'Finance'
  })
  @IsString()
  @IsOptional()
  function?: string;

  @ApiPropertyOptional({
    description: 'How often this deliverable is generated or updated',
    example: 'Weekly'
  })
  @IsString()
  @IsOptional()
  frequency?: string;

  @ApiPropertyOptional({
    description: 'Source system or process that generates this deliverable',
    example: 'SAP Financial System v2'
  })
  @IsString()
  @IsOptional()
  source?: string;

  @ApiPropertyOptional({
    description: 'Whether this deliverable is currently active and in use',
    example: false
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiPropertyOptional({
    description: 'List of owners responsible for this deliverable',
    type: [OwnerDto],
    example: [
      { name: 'Jane Smith', email: '<EMAIL>', role: 'Senior Analyst' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OwnerDto)
  @IsOptional()
  owners?: OwnerDto[];

  @ApiPropertyOptional({
    description: 'How this deliverable is aggregated at business unit level',
    example: 'Average across all BUs'
  })
  @IsString()
  @IsOptional()
  bu_level_aggregation?: string;

  @ApiPropertyOptional({
    description: 'Method or formula used to calculate this deliverable',
    example: 'Updated calculation: Sum of revenue streams with new discount logic'
  })
  @IsString()
  @IsOptional()
  calculation_method?: string;

  @ApiPropertyOptional({
    description: 'Detailed definition and purpose of this deliverable',
    example: 'Updated monthly report showing total revenue with enhanced analytics'
  })
  @IsString()
  @IsOptional()
  definition?: string;

  @ApiPropertyOptional({
    description: 'Performance Analytics value or target for this deliverable',
    example: '$12M monthly target (updated)'
  })
  @IsString()
  @IsOptional()
  pa_value?: string;
}