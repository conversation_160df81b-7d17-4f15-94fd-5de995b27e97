import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsDate, IsArray } from 'class-validator';
import { Type } from 'class-transformer';
import { ProjectType } from '../../enums';

export class UpdateProjectRequest {
  @ApiPropertyOptional({
    description: 'Name of the project',
    example: 'Q4 Revenue Optimization Initiative - Phase 2'
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Business function or department this project belongs to',
    example: 'Operations'
  })
  @IsString()
  @IsOptional()
  function?: string;

  @ApiPropertyOptional({
    description: 'Business objective or goal this project aims to achieve',
    example: 'Increase quarterly revenue by 20% through enhanced process optimization'
  })
  @IsString()
  @IsOptional()
  objective?: string;

  @ApiPropertyOptional({
    description: 'Classification type of this project',
    enum: ProjectType,
    example: ProjectType.MASTER
  })
  @IsEnum(ProjectType)
  @IsOptional()
  project_type?: ProjectType;

  @ApiPropertyOptional({
    description: 'Updated project start date',
    type: Date,
    example: '2024-02-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_start?: Date;

  @ApiPropertyOptional({
    description: 'Updated project end date',
    type: Date,
    example: '2025-01-31T23:59:59.999Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_end?: Date;

  @ApiPropertyOptional({
    description: 'Updated array of deliverable UIDs that are part of this project',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e12b-34c5-d678-901234567def']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  deliverables?: string[];
}
