import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsDate,
  IsEnum,
  IsOptional,
  IsString,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProjectType } from '../../enums';

export class CreateProjectRequest {
  @ApiProperty({
    description: 'Name of the project',
    example: 'Q4 Revenue Optimization Initiative'
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Business function or department this project belongs to',
    example: 'Finance'
  })
  @IsOptional()
  @IsString()
  function?: string;

  @ApiPropertyOptional({
    description: 'Business objective or goal this project aims to achieve',
    example: 'Increase quarterly revenue by 15% through process optimization'
  })
  @IsOptional()
  @IsString()
  objective?: string;

  @ApiProperty({
    description: 'Classification type of this project',
    enum: ProjectType,
    example: ProjectType.SIMPLE
  })
  @IsEnum(ProjectType)
  project_type: ProjectType;

  @ApiPropertyOptional({
    description: 'Project start date',
    type: Date,
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_start?: Date;

  @ApiPropertyOptional({
    description: 'Project end date',
    type: Date,
    example: '2024-12-31T23:59:59.999Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_end?: Date;

  @ApiPropertyOptional({
    description: 'Array of deliverable UIDs that are part of this project',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '987fcdeb-51a2-43d1-9f4e-123456789abc']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  deliverables?: string[];
}
