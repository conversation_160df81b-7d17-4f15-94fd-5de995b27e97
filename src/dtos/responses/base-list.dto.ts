import { ApiProperty } from '@nestjs/swagger';
import { PaginatedRequest } from '../requests/paginated.dto';

export class BaseListResponse<T> extends PaginatedRequest {
  @ApiProperty({
    description: 'Total number of records available (across all pages)',
    example: 150
  })
  totalRecords: number;

  @ApiProperty({
    description: 'Array of data items for the current page',
    isArray: true
  })
  data: Array<T>;
}
