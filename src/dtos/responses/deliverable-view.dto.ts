import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OwnerDto } from 'src/dtos/owner.dto';

export class DeliverableViewDto {
  @ApiProperty({
    description: 'Unique identifier for the deliverable or project',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  uid: string;

  @ApiProperty({
    description: 'Name of the deliverable or project',
    example: 'Monthly Revenue Report'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Business function or department',
    example: 'Finance'
  })
  @IsString()
  function: string;

  @ApiProperty({
    description: 'Frequency of generation or update',
    example: 'Monthly'
  })
  @IsString()
  frequency: string;

  @ApiProperty({
    description: 'Whether the item is currently active',
    example: true
  })
  @IsBoolean()
  is_active: boolean;

  @ApiProperty({
    description: 'Usage score or priority level',
    example: 85
  })
  @IsNumber()
  usage: number;

  @ApiProperty({
    description: 'Source system or process',
    example: 'SAP Financial System'
  })
  @IsString()
  source: string;

  @ApiPropertyOptional({
    description: 'List of owners responsible for this deliverable',
    type: [OwnerDto],
    example: [
      { name: 'John Doe', email: '<EMAIL>', role: 'Data Analyst' }
    ]
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OwnerDto)
  owners?: OwnerDto[];

  @ApiPropertyOptional({
    description: 'Method or formula used to calculate this deliverable',
    example: 'Sum of all revenue streams minus returns and discounts'
  })
  @IsOptional()
  @IsString()
  calculation_method?: string;

  @ApiPropertyOptional({
    description: 'Detailed definition and purpose of this deliverable',
    example: 'Monthly report showing total revenue generated across all business units'
  })
  @IsOptional()
  @IsString()
  definition?: string;

  @ApiPropertyOptional({
    description: 'Performance Analytics value or target',
    example: '$10M monthly target'
  })
  @IsOptional()
  @IsString()
  pa_value?: string;

  @ApiPropertyOptional({
    description: 'Type classification of this deliverable',
    example: 'KPI'
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'How this deliverable is aggregated at business unit level',
    example: 'Sum across all BUs'
  })
  @IsOptional()
  @IsString()
  bu_level_aggregation?: string;

  @ApiPropertyOptional({
    description: 'Business objective or goal this deliverable supports',
    example: 'Increase quarterly revenue by 15%'
  })
  @IsOptional()
  @IsString()
  objective?: string;

  @ApiPropertyOptional({
    description: 'Type of project this deliverable belongs to (if applicable)',
    example: 'STRATEGIC'
  })
  @IsOptional()
  @IsString()
  project_type?: string;

  @ApiPropertyOptional({
    description: 'Associated deliverables (for projects) or sub-deliverables',
    type: [DeliverableViewDto],
    example: []
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DeliverableViewDto)
  deliverables?: DeliverableViewDto[];

  @ApiPropertyOptional({
    description: 'Start date for this deliverable (if time-bound)',
    type: Date,
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_start?: Date;

  @ApiPropertyOptional({
    description: 'End date for this deliverable (if time-bound)',
    type: Date,
    example: '2024-12-31T23:59:59.999Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  date_end?: Date;
}
