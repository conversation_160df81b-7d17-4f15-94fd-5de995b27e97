import { Origins } from '../types';
import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';

export abstract class BaseEntity {
  @PrimaryColumn({ type: 'uniqueidentifier', default: () => 'NEWID()' })
  uid: string;

  @DeleteDateColumn({ type: 'datetime2', nullable: true })
  deleted_at?: Date;

  @CreateDateColumn({ type: 'datetime2' })
  created_at: Date;

  @UpdateDateColumn({ type: 'datetime2', nullable: true })
  updated_at?: Date;

  @Column({ type: 'uniqueidentifier', nullable: true })
  deleted_by?: string;

  @Column({ type: 'uniqueidentifier' })
  created_by: string;

  @Column({ type: 'uniqueidentifier', nullable: true })
  updated_by?: string;

  @Column({ type: 'nvarchar', length: 255 })
  origin: `${Origins}`;
}