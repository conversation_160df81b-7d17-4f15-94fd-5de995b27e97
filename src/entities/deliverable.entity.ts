import {
  <PERSON><PERSON><PERSON>,
  Column,
  ManyToOne,
  OneToMany,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { DeliverableTypeEntity } from './deliverable-type.entity';
import { ProjectEntity } from './project.entity';
import { BaseEntity } from './base.entity';
import { DeliverableUsageEntity } from './deliverable-usage.entity';

@Entity({ name: 'kpi_deliverable' })
export class DeliverableEntity extends BaseEntity {
  @Column()
  name: string;

  @Column()
  function: string;

  @Column()
  frequency: string;

  @Column()
  is_active: boolean;

  @Column()
  source: string;

  @Column()
  usage: number;

  @Column({ type: 'nvarchar', length: 'MAX' })
  calculation_method: string;

  @Column({ type: 'nvarchar', length: 'MAX' })
  definition: string;

  @Column({ type: 'nvarchar', length: 'MAX' })
  pa_value: string;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  bu_level_aggregation: string;

  @Column()
  is_cascading: boolean;

  @ManyToOne(() => DeliverableTypeEntity, (type) => type.deliverables)
  @JoinColumn({ name: 'deliverable_type_code' })
  deliverable_type: DeliverableTypeEntity;

  @ManyToOne(() => ProjectEntity, (project) => project.deliverables)
  @JoinColumn({ name: 'origin_project_code' })
  origin_project: ProjectEntity;

  @OneToMany(() => DeliverableUsageEntity, (usage) => usage.deliverable)
  usages: DeliverableUsageEntity[];
}
