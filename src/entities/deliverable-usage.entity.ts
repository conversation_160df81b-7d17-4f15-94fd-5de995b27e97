import { <PERSON><PERSON>ty, <PERSON>umn, PrimaryColumn, ManyToOne, <PERSON>inC<PERSON>um<PERSON> } from 'typeorm';
import { DeliverableEntity } from './deliverable.entity';
import { BaseEntity } from './base.entity';

@Entity({ name: 'kpi_deliverable_usage' })
export class DeliverableUsageEntity extends BaseEntity {
  @PrimaryColumn({ name: 'code', type: 'uniqueidentifier', default: () => 'NEWID()' })
  uid: string;

  @ManyToOne(() => DeliverableEntity, (d) => d.usages)
  @JoinColumn({ name: 'deliverable_code' })
  deliverable: DeliverableEntity;

  @Column()
  used_by: string;
}
