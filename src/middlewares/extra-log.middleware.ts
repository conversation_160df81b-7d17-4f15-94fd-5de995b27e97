import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction } from 'express';

@Injectable()
export class ExtraLogMiddleware implements NestMiddleware {
  constructor() {}

  use(req, _, next: NextFunction) {
    const dto = req.body;
    const method = req.method;
    const route = req.path;
    const user = req.session.user;
    const date = new Date().toISOString();
    if (user) {
      Logger.log(
        `[ExtraLog] Method: ${method} - Route: ${route} - ${date} - Token: ${JSON.stringify(
          user ?? {},
        )} - DTO: ${JSON.stringify(dto)}`,
      );
    } else {
      Logger.log(
        `[ExtraLog] Method: ${method} - Route: ${route} - ${date} - DTO: ${JSON.stringify(dto ?? {})}`,
      );
    }

    next();
  }
}
