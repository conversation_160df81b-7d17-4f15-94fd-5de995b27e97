import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProjectsController } from './projects.controller';
import { ProjectsService } from './projects.service';
import { ProjectEntity } from '../../entities/project.entity';
import { DeliverableEntity } from '../../entities/deliverable.entity';
import { ProjectRepository } from '../../repositories/project.repository';
import { DeliverableRepository } from '../../repositories/deliverable.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([ProjectEntity, DeliverableEntity]),
  ],
  controllers: [ProjectsController],
  providers: [ProjectsService, ProjectRepository, DeliverableRepository],
  exports: [ProjectsService],
})
export class ProjectsModule { }
