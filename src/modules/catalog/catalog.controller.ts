import {
  Controller,
  Get,
  Param,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { User, UserSession } from '../../decorators/user.decorator';
import { GetDeliverablesRequest } from '../../dtos/requests/get-deliverables.dto';
import { GetDeliverablesCompactResponse } from '../../dtos/responses/get-deliverables-compact.dto';
import { GetDeliverableResponse } from '../../dtos/responses/get-deliverable.dto';
import { CatalogService } from './catalog.service';

@ApiTags('Catalog')
@Controller('catalog')
export class CatalogController {
  constructor(private readonly catalogService: CatalogService) { }

  @Get(':uid')
  @ApiOperation({
    summary: 'Get Deliverable or Project by UID',
    description: 'Retrieves a specific deliverable or project by its unique identifier. The endpoint searches both deliverables and projects and returns the first match found.'
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable or project to retrieve',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the deliverable or project',
    type: 'GetDeliverableResponse'
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable or project not found'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  async getDeliverableOrProject(
    @Param('uid') uid: string,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.catalogService.getDeliverableOrProject(uid, user?.uuid);
  }

  @Get()
  @ApiOperation({
    summary: 'Search Deliverables and Projects',
    description: 'Retrieves a filtered list of deliverables and projects based on search criteria including text search, year, functions, active status, and type filters'
  })
  @ApiQuery({
    name: 'search',
    description: 'Text search term to filter by name',
    required: false,
    type: 'string',
    example: 'revenue'
  })
  @ApiQuery({
    name: 'year',
    description: 'Year filter for projects (based on start date)',
    required: true,
    type: 'number',
    example: 2024
  })
  @ApiQuery({
    name: 'functions',
    description: 'Array of function names to filter by',
    required: false,
    type: 'array',
    items: { type: 'string' },
    example: ['Finance', 'Marketing']
  })
  @ApiQuery({
    name: 'is_active',
    description: 'Filter by active status',
    required: false,
    type: 'boolean',
    example: true
  })
  @ApiQuery({
    name: 'type',
    description: 'Filter by entity type (Deliverable, Project, or both)',
    required: false,
    type: 'array',
    items: { type: 'string', enum: ['Deliverable', 'Project'] },
    example: ['Deliverable', 'Project']
  })
  @ApiQuery({
    name: 'pageNumber',
    description: 'Page number for pagination',
    required: true,
    type: 'number',
    example: 1
  })
  @ApiQuery({
    name: 'pageSize',
    description: 'Number of items per page',
    required: true,
    type: 'number',
    example: 10
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved filtered deliverables and projects',
    type: 'GetDeliverablesCompactResponse'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid query parameters'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  async getDeliverablesAndProjects(
    @Query() filters: GetDeliverablesRequest,
    @User() user: UserSession,
  ): Promise<GetDeliverablesCompactResponse> {
    return this.catalogService.getDeliverablesAndProjects(filters, user?.uuid);
  }
}
