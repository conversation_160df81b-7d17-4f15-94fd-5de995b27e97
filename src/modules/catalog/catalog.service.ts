import {
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { GetDeliverablesRequest } from '../../dtos/requests/get-deliverables.dto';
import { GetDeliverablesCompactResponse } from '../../dtos/responses/get-deliverables-compact.dto';
import { ProjectRepository } from '../../repositories/project.repository';
import { mapToView } from '../../mappers/deliverable.mapper';
import { DeliverableRepository } from '../../repositories/deliverable.repository';
import { GetDeliverableResponse } from '../../dtos/responses/get-deliverable.dto';

@Injectable()
export class CatalogService {
  constructor(
    private readonly deliverableRepository: DeliverableRepository,
    private readonly projectRepository: ProjectRepository,
  ) { }

  async getDeliverableOrProject(
    uid: string,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponse> {
    console.log(sessionUserUuid);

    const project = await this.projectRepository.findByUid(uid);
    if (project) return mapToView(project);

    const deliverable = await this.deliverableRepository.findByUid(uid);
    if (deliverable) return mapToView(deliverable);

    throw new NotFoundException(`KPI or Project with uid ${uid} not found.`);
  }

  async getDeliverablesAndProjects(
    filters: GetDeliverablesRequest,
    sessionUserUuid: string,
  ): Promise<GetDeliverablesCompactResponse> {
    console.log(sessionUserUuid);

    const normalizedFilters = this.normalizeFilters(filters);
    const { search, year, functions, is_active, type } = normalizedFilters;

    const fetchOptions = this.determineFetchOptions(type);
    const results = await this.fetchFilteredData(
      fetchOptions,
      search,
      year,
      functions,
      is_active,
    );

    return this.combineResults(results);
  }

  private normalizeFilters(
    filters: GetDeliverablesRequest,
  ): GetDeliverablesRequest {
    if (typeof filters.functions === 'string') {
      try {
        filters.functions = JSON.parse(filters.functions);
      } catch {
        filters.functions = [];
      }
    }
    return filters;
  }

  private determineFetchOptions(type?: string[]): {
    fetchProjects: boolean;
    fetchDeliverables: boolean;
  } {
    const fetchProjects = type ? type.includes('Project') : true;
    const fetchDeliverables = type ? type.includes('Deliverable') : true;
    return { fetchProjects, fetchDeliverables };
  }

  private async fetchFilteredData(
    {
      fetchProjects,
      fetchDeliverables,
    }: { fetchProjects: boolean; fetchDeliverables: boolean },
    search?: string,
    year?: number,
    functions?: string[],
    is_active?: boolean,
  ): Promise<[GetDeliverablesCompactResponse, GetDeliverablesCompactResponse]> {
    const emptyResult = {
      data: [],
      pageNumber: 1,
      pageSize: 0,
      totalRecords: 0,
    };

    if (!fetchProjects && !fetchDeliverables) {
      return [emptyResult, emptyResult];
    }

    if (fetchProjects && fetchDeliverables) {
      return Promise.all([
        this.projectRepository.findCompactWithFilters(
          search,
          year,
          functions,
          is_active,
        ),
        this.deliverableRepository.findCompactStandaloneWithFilters(
          search,
          functions,
          is_active,
        ),
      ]);
    }

    if (fetchProjects) {
      const projects = await this.projectRepository.findCompactWithFilters(
        search,
        year,
        functions,
        is_active,
      );
      return [projects, emptyResult];
    }

    const deliverables =
      await this.deliverableRepository.findCompactStandaloneWithFilters(
        search,
        functions,
        is_active,
      );
    return [emptyResult, deliverables];
  }

  private combineResults([projects, deliverables]: [
    GetDeliverablesCompactResponse,
    GetDeliverablesCompactResponse,
  ]): GetDeliverablesCompactResponse {
    return {
      data: [...projects.data, ...deliverables.data],
      pageNumber: 1,
      pageSize: projects.data.length + deliverables.data.length,
      totalRecords: projects.totalRecords + deliverables.totalRecords,
    };
  }
}
