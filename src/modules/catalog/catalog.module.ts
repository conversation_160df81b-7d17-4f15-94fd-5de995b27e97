import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CatalogController } from './catalog.controller';
import { CatalogService } from './catalog.service';
import { DeliverableEntity } from '../../entities/deliverable.entity';
import { ProjectEntity } from '../../entities/project.entity';
import { ProjectRepository } from '../../repositories/project.repository';
import { DeliverableRepository } from '../../repositories/deliverable.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([DeliverableEntity, ProjectEntity]),
  ],
  controllers: [CatalogController],
  providers: [CatalogService, ProjectRepository, DeliverableRepository],
  exports: [CatalogService],
})
export class CatalogModule { }
