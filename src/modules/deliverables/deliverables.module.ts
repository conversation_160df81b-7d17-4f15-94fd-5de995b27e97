import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DeliverablesController } from './deliverables.controller';
import { DeliverablesService } from './deliverables.service';
import { DeliverableEntity } from '../../entities/deliverable.entity';
import { DeliverableRepository } from '../../repositories/deliverable.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([DeliverableEntity]),
  ],
  controllers: [DeliverablesController],
  providers: [DeliverablesService, DeliverableRepository],
  exports: [DeliverablesService],
})
export class DeliverablesModule { }
