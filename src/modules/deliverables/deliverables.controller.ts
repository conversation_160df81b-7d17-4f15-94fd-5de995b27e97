import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { User, UserSession } from '../../decorators/user.decorator';
import { CreateDeliverableRequest } from '../../dtos/requests/create-deliverable.dto';
import { UpdateDeliverableRequest } from '../../dtos/requests/update-deliverable.dto';
import { GetDeliverableResponse } from '../../dtos/responses/get-deliverable.dto';
import { GetDeliverablesResponse } from '../../dtos/responses/get-deliverables.dto';
import { DeliverablesService } from './deliverables.service';

@ApiTags('Deliverables')
@Controller('deliverables')
export class DeliverablesController {
  constructor(private readonly deliverablesService: DeliverablesService) { }

  @Get()
  @ApiOperation({
    summary: 'Get All Deliverables',
    description: 'Retrieves a complete list of all deliverables in the catalog with full details including metadata, owners, and relationships'
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all deliverables',
    type: 'GetDeliverablesResponse'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  async getAll(@User() user: UserSession): Promise<GetDeliverablesResponse> {
    return this.deliverablesService.getAll(user?.uuid);
  }

  @Get('functions')
  @ApiOperation({
    summary: 'Get Deliverable Functions',
    description: 'Retrieves a list of all unique function values used by deliverables in the catalog'
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved deliverable functions',
    schema: {
      type: 'array',
      items: {
        type: 'string'
      },
      example: ['Finance', 'Marketing', 'Operations', 'Sales']
    }
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  async getDeliverablesFunctions(): Promise<string[]> {
    return this.deliverablesService.getDeliverablesFunctions();
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create Deliverable',
    description: 'Creates a new deliverable in the catalog with the provided details'
  })
  @ApiBody({
    type: CreateDeliverableRequest,
    description: 'Deliverable creation data'
  })
  @ApiResponse({
    status: 201,
    description: 'Deliverable successfully created',
    type: 'GetDeliverableResponse'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  async createDeliverable(
    @Body() kpiDto: CreateDeliverableRequest,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.deliverablesService.createDeliverable(kpiDto, user?.uuid);
  }

  @Put(':uid')
  @ApiOperation({
    summary: 'Update Deliverable',
    description: 'Updates an existing deliverable with the provided data'
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable to update',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({
    type: UpdateDeliverableRequest,
    description: 'Deliverable update data'
  })
  @ApiResponse({
    status: 200,
    description: 'Deliverable successfully updated',
    type: 'GetDeliverableResponse'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data'
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  async updateDeliverable(
    @Param('uid') uid: string,
    @Body() kpiDto: UpdateDeliverableRequest,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.deliverablesService.updateDeliverable(uid, kpiDto, user?.uuid);
  }

  @Patch(':uid/:status')
  @ApiOperation({
    summary: 'Switch Deliverable Status',
    description: 'Toggles the active status of a deliverable'
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiParam({
    name: 'status',
    description: 'New status for the deliverable',
    type: 'boolean',
    example: true
  })
  @ApiResponse({
    status: 200,
    description: 'Deliverable status successfully updated',
    type: 'GetDeliverableResponse'
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found'
  })
  @ApiResponse({
    status: 501,
    description: 'Not implemented yet'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  switchDeliverableStatus(
    @Param('uid') uid: string,
    @Param('status') status: boolean,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.deliverablesService.switchDeliverableStatus(uid, status, user?.uuid);
  }

  @Delete(':uid')
  @ApiOperation({
    summary: 'Soft Delete Deliverable',
    description: 'Performs a soft delete on a deliverable (marks as deleted without removing from database)'
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the deliverable to delete',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: 200,
    description: 'Deliverable successfully soft deleted',
    type: 'GetDeliverableResponse'
  })
  @ApiResponse({
    status: 404,
    description: 'Deliverable not found'
  })
  @ApiResponse({
    status: 501,
    description: 'Not implemented yet'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  softDeleteDeliverable(
    @Param('uid') uid: string,
    @User() user: UserSession,
  ): Promise<GetDeliverableResponse> {
    return this.deliverablesService.softDeleteDeliverable(uid, user?.uuid);
  }
}
