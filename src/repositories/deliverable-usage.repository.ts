import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeliverableUsageEntity } from '../entities/deliverable-usage.entity';
import { BaseRepository } from './base.repository';

@Injectable()
export class DeliverableUsageRepository extends BaseRepository<DeliverableUsageEntity> {
  constructor(
    @InjectRepository(DeliverableUsageEntity)
    public repository: Repository<DeliverableUsageEntity>,
  ) {
    super(repository);
  }

  async findByDeliverableUid(uid: string): Promise<DeliverableUsageEntity[]> {
    return this.repository.find({
      where: { deliverable: { uid } },
      relations: ['deliverable'],
    });
  }
}
