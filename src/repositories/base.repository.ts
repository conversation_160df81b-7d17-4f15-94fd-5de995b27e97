import { BaseEntity } from '../entities/base.entity';
import { FindOptionsWhere, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

export abstract class BaseRepository<T extends BaseEntity> {
  constructor(protected repo: Repository<T>) { }

  /**
   * Creates an entity
   * @param entity T
   * @returns T
   */
  create(entity: T): Promise<T> {
    return this.repo.save(entity);
  }

  /**
   * Updates an entity
   * @param primaryKey string|number
   * @param entity T
   * @returns T
   */
  update = async (
    primaryKeyColumn: string,
    primaryKeyValue: string | number,
    entity: any,
  ) => {
    const existingEntity = await this.repo.findOneBy({
      [primaryKeyColumn]: primaryKeyValue,
    } as unknown as FindOptionsWhere<T>);
    const updatedEntity = { ...existingEntity, ...entity };
    return await this.repo.save(updatedEntity);
  };

  /**
   * Updates an entity
   * @param primaryKey string|number
   * @param entity T
   * @returns T
   */
  updateByFilter = (
    options: FindOptionsWhere<T>,
    entity: QueryDeepPartialEntity<T>,
  ) => this.repo.update(options, entity);

  /**
   * Deletes an entity by id
   * @param id string|number
   * @param uid_employee_deleted string
   */
  async delete(
    id: string | number,
    uid_employee_deleted: string,
  ): Promise<void> {
    await this.repo.update(id, {
      uid_employee_deleted,
      dat_deleted: new Date().toISOString(),
    } as unknown as QueryDeepPartialEntity<T>);
  }

  /**
     * Finds all entities from the repository
     * 
     * @returns Promise<T[]> A list of all entities
     */
  async find(): Promise<T[]> {
    return this.repo.find();
  }
}
