import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeliverableEntity } from '../entities/deliverable.entity';
import { BaseRepository } from './base.repository';
import { GetDeliverablesCompactResponse } from 'src/dtos/responses/get-deliverables-compact.dto';

@Injectable()
export class DeliverableRepository extends BaseRepository<DeliverableEntity> {
  constructor(
    @InjectRepository(DeliverableEntity)
    public repository: Repository<DeliverableEntity>,
  ) {
    super(repository);
  }

  async findByUid(uid: string): Promise<DeliverableEntity | null> {
    return this.repository.findOne({
      where: { uid },
      relations: ['deliverable_type', 'origin_project', 'usages'],
    });
  }

  async findCompactStandaloneWithFilters(
    search?: string,
    functions?: string[],
    is_active?: boolean,
  ): Promise<GetDeliverablesCompactResponse> {
    const query = this.repository
      .createQueryBuilder('deliverable')
      .leftJoin('deliverable.origin_project', 'project')
      .leftJoin('deliverable.deliverable_type', 'deliverable_type');

    if (is_active !== undefined) {
      query.andWhere('deliverable.is_active = :is_active', {
        is_active: is_active ? 1 : 0,
      });
    } else {
      query.andWhere('deliverable.is_active = 1');
    }

    if (search) {
      query.andWhere('deliverable.name LIKE :search', {
        search: `%${search}%`,
      });
    }

    if (functions) {
      query.andWhere('deliverable.function IN (:...functions)', {
        functions,
      });
    }

    query.orderBy('deliverable.name', 'ASC');

    const entities = await query
      .select([
        'deliverable.uid',
        'deliverable.name',
        'deliverable.function',
        'deliverable.usage',
        'deliverable_type.code',
        'deliverable.is_active',
      ])
      .addSelect('deliverable_type.code', 'type')
      .getMany();

    return {
      data: entities.map((d) => ({
        uid: d.uid,
        name: d.name,
        function: d.function,
        type: d.deliverable_type?.code ?? '',
        usage: d.usage ?? 0,
        is_active: d.is_active,
      })),
      pageNumber: 1,
      pageSize: entities.length,
      totalRecords: entities.length,
    };
  }

  async findByUids(uids: string[]): Promise<DeliverableEntity[]> {
    if (!uids || uids.length === 0) {
      return [];
    }

    return this.repository
      .createQueryBuilder('deliverable')
      .select(['deliverable.uid', 'deliverable.name'])
      .where('deliverable.uid IN (:...uids)', { uids })
      .getMany();
  }

  async getFunctions(): Promise<{ function: string }[]> {
    const result = await this.repository
      .createQueryBuilder('deliverable')
      .select('deliverable.function', 'function')
      .where('deliverable.function IS NOT NULL')
      .andWhere('deliverable.function != :emptyString', { emptyString: '' })
      .getRawMany();

    console.log('Functions retrieved:', result);
    return result;
  }
}
