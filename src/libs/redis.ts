import { createClient } from 'redis';

export const redis = createClient({
  url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`,
});

type CacheValue = object | string | number;

redis.on('error', (error) => {
  console.error('Redis error', error, error.stack);
});

export const expireCache = async (key: string, time: number) => {
  await redis.expire(`key:${key}`, time);
};

const isNumber = (numberValue) =>
  typeof numberValue !== 'undefined' &&
  numberValue !== null &&
  String(numberValue).trim().length > 0 &&
  (numberValue == 0 || isFinite(numberValue));

export const setCache = async (
  key: string,
  value: CacheValue,
  expirationTime?: number,
) => {
  await redis.set(`key:${key}`, JSON.stringify(value));

  if (isNumber(expirationTime)) {
    await expireCache(key, expirationTime as number);
  }
};

export const getCache = async (key: string) => {
  const result = await redis.get(`key:${key}`);

  if (!result) {
    return null;
  }

  try {
    return JSON.parse(result);
  } catch (error) {
    return result;
  }
};

export const deleteCache = async (key: string) => {
  await redis.del(`key:${key}`);
};

console.log('Starting Redis connection');
redis.connect();
console.log('Redis connection started');
