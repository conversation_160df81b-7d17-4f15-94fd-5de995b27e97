import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Public } from './decorators/public.decorator';
import { AppService } from './app.service';

@ApiTags('Health Check')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Get('health-check')
  @Public()
  @ApiOperation({
    summary: 'Health Check',
    description: 'Returns the health status of the KPI Catalog API service'
  })
  @ApiResponse({
    status: 200,
    description: 'Service is healthy',
    schema: {
      type: 'string',
      example: 'OK'
    }
  })
  getHealthCheck(): string {
    return this.appService.getHealthCheck();
  }
}
