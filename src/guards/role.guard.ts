import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { IS_PUBLIC_KEY } from 'src/contants';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    //Checking if we have @Public annotation
    const isPublic = this.reflector.get<number>(
      IS_PUBLIC_KEY,
      context.getHandler(),
    );

    if (isPublic) {
      return true;
    }

    const requiredRoles = this.reflector.getAllAndOverride<string[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );
    if (!requiredRoles) {
      return true;
    }
    const {
      session: { user },
    } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) =>
      user.roles_json?.some(
        (userRole) => userRole.toUpperCase() === role.toUpperCase(),
      ),
    );
  }
}
